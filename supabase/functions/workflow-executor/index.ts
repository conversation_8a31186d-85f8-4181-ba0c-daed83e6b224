import { createClient } from 'npm:@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
}

interface ExecutionRequest {
  action: 'trigger' | 'process_pending' | 'retry_failed';
  triggerData?: {
    entity_type: 'lead' | 'case';
    entity_id: string;
    old_status?: string;
    new_status: string;
    company_id: string;
  };
  executionId?: string;
}

const handler = async (req: Request): Promise<Response> => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const body: ExecutionRequest = await req.json();
    const { action } = body;

    switch (action) {
      case 'trigger':
        if (!body.triggerData) {
          throw new Error('Trigger data required');
        }
        return await triggerWorkflows(supabaseAdmin, body.triggerData);

      case 'trigger_manual':
        if (!body.manualTriggerData) {
          throw new Error('Manual trigger data required');
        }
        return await triggerManualWorkflow(supabaseAdmin, body.manualTriggerData);

      case 'process_pending':
        return await processPendingExecutions(supabaseAdmin);

      case 'retry_failed':
        if (!body.executionId) {
          throw new Error('Execution ID required');
        }
        return await retryFailedExecution(supabaseAdmin, body.executionId);

      case 'continue':
        if (!body.executionId) {
          throw new Error('Execution ID required');
        }
        return await continueExecution(supabaseAdmin, body.executionId);

      default:
        throw new Error('Invalid action');
    }

  } catch (error) {
    console.error('Error in workflow-executor:', error);
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message || 'Internal server error' 
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
};

async function triggerWorkflows(supabaseAdmin: any, triggerData: any) {
  const { entity_type, entity_id, old_status, new_status, company_id, trigger_type } = triggerData;

  console.log(`Triggering workflows for ${entity_type} ${entity_id}: ${old_status} -> ${new_status}`);

  // Determine trigger type based on entity and trigger_type
  let workflowTriggerType;
  if (trigger_type === 'lead_answer_status_change') {
    workflowTriggerType = 'lead_answer_status_change';
  } else {
    workflowTriggerType = entity_type === 'lead' ? 'lead_status_change' : 'case_status_change';
  }
  
  const { data: workflows, error: workflowsError } = await supabaseAdmin
    .from('workflows')
    .select(`
      *,
      workflow_steps (
        id,
        step_order,
        step_type,
        step_config
      )
    `)
    .eq('company_id', company_id)
    .eq('trigger_type', workflowTriggerType)
    .eq('is_active', true);

  if (workflowsError) throw workflowsError;

  const matchingWorkflows = workflows.filter((workflow: any) => {
    const config = workflow.trigger_config;
    const fromMatch = config.from_status === 'any' || config.from_status === old_status;

    // Handle pattern matching for answer status (e.g., "לא ענה*")
    let toMatch;
    if (config.to_status.endsWith('*')) {
      const pattern = config.to_status.slice(0, -1); // Remove the *
      toMatch = new_status.startsWith(pattern);
    } else {
      toMatch = config.to_status === new_status;
    }

    return fromMatch && toMatch;
  });

  console.log(`Found ${matchingWorkflows.length} matching workflows`);

  // Create executions for matching workflows
  const executions = [];
  for (const workflow of matchingWorkflows) {
    console.log(`Creating execution for workflow ${workflow.id}, steps:`, workflow.workflow_steps);

    // Determine lead_id based on entity type
    let leadId = entity_id;
    if (entity_type === 'case') {
      // If triggered by case, get lead ID from case
      const { data: caseData, error: caseError } = await supabaseAdmin
        .from('cases')
        .select('lead_id')
        .eq('id', entity_id)
        .single();

      if (caseError) {
        console.error('Error fetching case data:', caseError);
        continue;
      }
      leadId = caseData.lead_id;
    }

    const { data: execution, error: executionError } = await supabaseAdmin
      .from('workflow_executions')
      .insert({
        workflow_id: workflow.id,
        company_id: company_id,
        trigger_entity_type: entity_type,
        trigger_entity_id: entity_id,
        status: 'running',
        current_step_order: 1
      })
      .select()
      .single();

    if (executionError) {
      console.error('Error creating execution:', executionError);
      continue;
    }

    console.log('Execution created successfully:', execution.id);
    executions.push(execution);

    // Start executing the workflow
    if (workflow.workflow_steps && workflow.workflow_steps.length > 0) {
      console.log('Starting first step:', workflow.workflow_steps[0]);
      await executeWorkflowStep(supabaseAdmin, execution.id, workflow.workflow_steps[0]);
    } else {
      console.error('No workflow steps found for workflow:', workflow.id);
    }
  }

  return new Response(
    JSON.stringify({ 
      success: true, 
      triggered_workflows: matchingWorkflows.length,
      executions: executions.map(e => e.id)
    }),
    { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  );
}

async function processPendingExecutions(supabaseAdmin: any) {
  // Find executions that are ready to continue (wait steps that have expired)
  const { data: pendingExecutions, error } = await supabaseAdmin
    .from('workflow_executions')
    .select(`
      *,
      workflows!inner (
        id,
        workflow_steps (
          id,
          step_order,
          step_type,
          step_config
        )
      )
    `)
    .eq('status', 'running')
    .not('next_execution_at', 'is', null)
    .lte('next_execution_at', new Date().toISOString());

  if (error) throw error;

  console.log(`Processing ${pendingExecutions.length} pending executions`);

  for (const execution of pendingExecutions) {
    const workflow = execution.workflows;
    const currentStep = workflow.workflow_steps.find(
      (step: any) => step.step_order === execution.current_step_order
    );

    if (currentStep) {
      await executeWorkflowStep(supabaseAdmin, execution.id, currentStep);
    }
  }

  return new Response(
    JSON.stringify({ 
      success: true, 
      processed_executions: pendingExecutions.length 
    }),
    { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  );
}

async function executeWorkflowStep(supabaseAdmin: any, executionId: string, step: any) {
  console.log(`Executing step ${step.step_order} (${step.step_type}) for execution ${executionId}`);

  // Create execution log
  const { data: log, error: logError } = await supabaseAdmin
    .from('workflow_execution_logs')
    .insert({
      execution_id: executionId,
      step_id: step.id,
      step_order: step.step_order,
      step_type: step.step_type,
      status: 'running',
      input_data: step.step_config
    })
    .select()
    .single();

  if (logError) {
    console.error('Error creating execution log:', logError);
    return;
  }

  try {
    let result;
    
    switch (step.step_type) {
      case 'send_whatsapp':
        result = await executeSendWhatsApp(supabaseAdmin, executionId, step.step_config);
        break;
      
      case 'wait':
        result = await executeWait(supabaseAdmin, executionId, step.step_config);
        break;
      
      case 'update_lead_status':
        result = await executeUpdateLeadStatus(supabaseAdmin, executionId, step.step_config);
        break;
      
      case 'update_case_status':
        result = await executeUpdateCaseStatus(supabaseAdmin, executionId, step.step_config);
        break;
      
      case 'create_case':
        result = await executeCreateCase(supabaseAdmin, executionId, step.step_config);
        break;
      
      default:
        throw new Error(`Unknown step type: ${step.step_type}`);
    }

    // Update log as completed
    await supabaseAdmin
      .from('workflow_execution_logs')
      .update({
        status: 'completed',
        output_data: result,
        completed_at: new Date().toISOString()
      })
      .eq('id', log.id);

    // Move to next step or complete execution
    await moveToNextStep(supabaseAdmin, executionId, step.step_order);

  } catch (error) {
    console.error(`Error executing step ${step.step_type}:`, error);

    // Update log as failed
    await supabaseAdmin
      .from('workflow_execution_logs')
      .update({
        status: 'failed',
        error_message: error.message,
        completed_at: new Date().toISOString()
      })
      .eq('id', log.id);

    // Get current retry count and increment it
    const { data: currentExecution } = await supabaseAdmin
      .from('workflow_executions')
      .select('retry_count')
      .eq('id', executionId)
      .single();

    // Mark execution as failed (will be retried later)
    await supabaseAdmin
      .from('workflow_executions')
      .update({
        status: 'failed',
        error_message: error.message,
        retry_count: (currentExecution?.retry_count || 0) + 1
      })
      .eq('id', executionId);
  }
}

async function executeSendWhatsApp(supabaseAdmin: any, executionId: string, config: any) {
  // Get execution context
  const { data: execution, error: execError } = await supabaseAdmin
    .from('workflow_executions')
    .select('trigger_entity_type, trigger_entity_id, company_id')
    .eq('id', executionId)
    .single();

  if (execError) throw execError;

  // Determine recipient type (default to 'lead' for backward compatibility)
  const recipient = config.recipient || 'lead';

  // Get lead data and entity data
  let leadData;
  let entityData;

  if (execution.trigger_entity_type === 'lead') {
    const { data: lead, error: leadError } = await supabaseAdmin
      .from('leads')
      .select('*')
      .eq('id', execution.trigger_entity_id)
      .single();

    if (leadError) throw leadError;
    leadData = lead;
    entityData = lead;
  } else {
    // Get case and its associated lead
    const { data: caseData, error: caseError } = await supabaseAdmin
      .from('cases')
      .select('*, lead:leads(*)')
      .eq('id', execution.trigger_entity_id)
      .single();

    if (caseError) throw caseError;
    leadData = caseData.lead;
    entityData = caseData;
  }

  if (!leadData) {
    throw new Error('No lead data found');
  }

  // Determine the target phone number based on recipient
  let targetPhone;
  let targetUserId;

  if (recipient === 'assigned_user') {
    // Get assigned user's phone number
    let assignedUserId;

    if (execution.trigger_entity_type === 'lead') {
      assignedUserId = leadData.assigned_user_id;
    } else if (execution.trigger_entity_type === 'case') {
      assignedUserId = entityData.assigned_user_id;
    }

    if (!assignedUserId) {
      throw new Error('No assigned user found for this entity');
    }

    // Get user data from auth.users
    const { data: userData, error: userError } = await supabaseAdmin.auth.admin.getUserById(assignedUserId);

    if (userError || !userData.user) {
      throw new Error('Assigned user not found');
    }

    // Get user's phone from user metadata or profile
    const userPhone = userData.user.user_metadata?.phone || userData.user.phone;

    if (!userPhone) {
      throw new Error('Assigned user has no phone number');
    }

    targetPhone = userPhone;
    targetUserId = assignedUserId;
  } else {
    // Send to lead (default behavior)
    if (!leadData.phone) {
      throw new Error('Lead phone number not found');
    }
    targetPhone = leadData.phone;
    targetUserId = null;
  }

  // Get assigned user data for variables (if needed)
  let assignedUserData = null;
  if (execution.trigger_entity_type === 'lead' && leadData.assigned_user_id) {
    const { data: userData, error: userError } = await supabaseAdmin.auth.admin.getUserById(leadData.assigned_user_id);
    if (!userError && userData.user) {
      assignedUserData = {
        full_name: userData.user.user_metadata?.full_name || '',
        email: userData.user.email || '',
        phone: userData.user.user_metadata?.phone || userData.user.phone || ''
      };
    }
  } else if (execution.trigger_entity_type === 'case' && entityData.assigned_user_id) {
    const { data: userData, error: userError } = await supabaseAdmin.auth.admin.getUserById(entityData.assigned_user_id);
    if (!userError && userData.user) {
      assignedUserData = {
        full_name: userData.user.user_metadata?.full_name || '',
        email: userData.user.email || '',
        phone: userData.user.user_metadata?.phone || userData.user.phone || ''
      };
    }
  }

  // Replace variables in message
  let message = config.message || '';

  // Lead variables
  message = message.replace(/\{lead_name\}/g, leadData.full_name || '');
  message = message.replace(/\{lead_phone\}/g, leadData.phone || '');
  message = message.replace(/\{lead_email\}/g, leadData.email || '');
  message = message.replace(/\{lead_status\}/g, leadData.status || '');
  message = message.replace(/\{lead_value\}/g, leadData.value ? `₪${leadData.value}` : '');
  message = message.replace(/\{lead_source\}/g, leadData.source || '');
  message = message.replace(/\{lead_notes\}/g, leadData.notes || '');

  // Company variables
  message = message.replace(/\{company_name\}/g, company.name || '');
  message = message.replace(/\{company_email\}/g, company.email || '');
  message = message.replace(/\{company_phone\}/g, company.phone || '');
  message = message.replace(/\{company_address\}/g, company.address || '');

  // Assigned user variables
  if (assignedUserData) {
    message = message.replace(/\{assigned_user_name\}/g, assignedUserData.full_name || '');
    message = message.replace(/\{assigned_user_email\}/g, assignedUserData.email || '');
    message = message.replace(/\{assigned_user_phone\}/g, assignedUserData.phone || '');
  } else {
    // Replace with empty strings if no assigned user
    message = message.replace(/\{assigned_user_name\}/g, '');
    message = message.replace(/\{assigned_user_email\}/g, '');
    message = message.replace(/\{assigned_user_phone\}/g, '');
  }

  // Format phone number for WhatsApp (ensure it's in the correct format)
  let formattedPhone = targetPhone.replace(/\D/g, ''); // Remove non-digits

  // Convert Israeli local format (0XX) to international format (972XX)
  if (formattedPhone.startsWith('0')) {
    formattedPhone = '972' + formattedPhone.substring(1);
  }
  // If it doesn't start with 972 and is 9-10 digits, assume it's Israeli
  else if (!formattedPhone.startsWith('972') && (formattedPhone.length === 9 || formattedPhone.length === 10)) {
    formattedPhone = '972' + formattedPhone;
  }

  console.log(`Formatted phone number: ${targetPhone} -> ${formattedPhone}`);

  // Get company data for variables and GreenAPI credentials
  const { data: company, error: companyError } = await supabaseAdmin
    .from('companies')
    .select('green_api_instance_id, green_api_token, name, email, phone, address')
    .eq('id', execution.company_id)
    .single();

  if (companyError) throw companyError;

  if (!company?.green_api_instance_id || !company?.green_api_token) {
    throw new Error('GreenAPI credentials not configured for company');
  }

  // Send message directly via GreenAPI (bypass the auth issue)
  const chatId = formattedPhone.startsWith('972') ? formattedPhone : `972${formattedPhone.substring(1)}`;
  const instanceSubdomain = company.green_api_instance_id.substring(0, 4);
  const greenApiUrl = `https://${instanceSubdomain}.api.greenapi.com/waInstance${company.green_api_instance_id}/sendMessage/${company.green_api_token}`;

  const greenApiResponse = await fetch(greenApiUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      chatId: `${chatId}@c.us`,
      message: message,
    }),
  });

  const greenApiResult = await greenApiResponse.json();
  console.log('GreenAPI response:', greenApiResult);

  if (!greenApiResponse.ok) {
    throw new Error(`GreenAPI error: ${greenApiResult.error || 'Unknown error'}`);
  }

  // Handle conversation creation based on recipient
  let conversation;

  if (recipient === 'assigned_user') {
    // For messages to assigned users, we still associate with the lead but note it's for the user
    // Find existing conversation by lead_id
    const { data: existingConv, error: convError } = await supabaseAdmin
      .from('whatsapp_conversations')
      .select('id')
      .eq('company_id', execution.company_id)
      .eq('lead_id', leadData.id)
      .single();

    if (!existingConv && !convError) {
      // Create new conversation for the lead (even though message goes to user)
      const { data: newConversation, error: insertError } = await supabaseAdmin
        .from('whatsapp_conversations')
        .insert({
          company_id: execution.company_id,
          lead_id: leadData.id,
          phone_number: formattedPhone,
          green_api_chat_id: `${chatId}@c.us`,
          contact_name: `${leadData.full_name} (via assigned user)`,
          last_message: message,
          last_message_timestamp: new Date().toISOString(),
        })
        .select('id')
        .single();

      if (!insertError) {
        conversation = newConversation;
      }
    } else {
      conversation = existingConv;
    }
  } else {
    // Original logic for lead messages
    const { data: existingConv, error: convError } = await supabaseAdmin
      .from('whatsapp_conversations')
      .select('id')
      .eq('company_id', execution.company_id)
      .eq('lead_id', leadData.id)
      .single();

    if (!existingConv && !convError) {
      // Try to create new conversation, handle potential unique constraint violation
      const { data: newConversation, error: insertError } = await supabaseAdmin
        .from('whatsapp_conversations')
        .insert({
          company_id: execution.company_id,
          lead_id: leadData.id,
          phone_number: formattedPhone,
          green_api_chat_id: `${chatId}@c.us`,
          contact_name: leadData.full_name,
          last_message: message,
          last_message_timestamp: new Date().toISOString(),
        })
        .select('id')
        .single();

      if (insertError && insertError.code === '23505') {
        // Unique constraint violation - conversation was created by another process
        // Fetch the existing conversation
        const { data: existingConv } = await supabaseAdmin
          .from('whatsapp_conversations')
          .select('id')
          .eq('company_id', execution.company_id)
          .eq('lead_id', leadData.id)
          .single();
        conversation = existingConv;
      } else if (!insertError) {
        conversation = newConversation;
      } else {
        throw insertError;
      }
    } else {
      conversation = existingConv;
    }
  }

  if (conversation) {
    // Update conversation with last message
    await supabaseAdmin
      .from('whatsapp_conversations')
      .update({
        last_message: message,
        last_message_timestamp: new Date().toISOString(),
      })
      .eq('id', conversation.id);
  }

  // Store the message
  await supabaseAdmin
    .from('whatsapp_messages')
    .insert({
      conversation_id: conversation!.id,
      green_api_message_id: greenApiResult.idMessage,
      content: message,
      message_type: 'text',
      sender_type: 'outgoing',
      status: 'sent',
    });

  return {
    message_sent: true,
    phone: formattedPhone,
    message,
    messageId: greenApiResult.idMessage,
    conversationId: conversation!.id,
    recipient: recipient,
    targetUserId: targetUserId
  };
}

async function executeWait(supabaseAdmin: any, executionId: string, config: any) {
  const { duration, unit } = config;
  
  // Calculate next execution time
  const now = new Date();
  let nextExecution = new Date(now);
  
  switch (unit) {
    case 'minutes':
      nextExecution.setMinutes(now.getMinutes() + duration);
      break;
    case 'hours':
      nextExecution.setHours(now.getHours() + duration);
      break;
    case 'days':
      nextExecution.setDate(now.getDate() + duration);
      break;
    default:
      throw new Error(`Invalid wait unit: ${unit}`);
  }

  // Update execution with next execution time
  await supabaseAdmin
    .from('workflow_executions')
    .update({
      next_execution_at: nextExecution.toISOString()
    })
    .eq('id', executionId);

  // Schedule immediate continuation using Edge Function with delay
  const delayMs = nextExecution.getTime() - Date.now();
  console.log(`Scheduling workflow continuation for ${executionId} in ${delayMs}ms`);

  // Use setTimeout equivalent for Edge Functions
  setTimeout(async () => {
    try {
      const response = await fetch(`${Deno.env.get('SUPABASE_URL')}/functions/v1/workflow-executor`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`
        },
        body: JSON.stringify({
          action: 'continue',
          executionId: executionId
        })
      });
      console.log(`Continued workflow execution ${executionId} with status ${response.status}`);
    } catch (error) {
      console.error(`Failed to continue workflow execution ${executionId}:`, error);
    }
  }, delayMs);

  return { wait_until: nextExecution.toISOString(), duration, unit, scheduled_delay_ms: delayMs };
}

async function executeUpdateLeadStatus(supabaseAdmin: any, executionId: string, config: any) {
  // Get execution context
  const { data: execution, error: execError } = await supabaseAdmin
    .from('workflow_executions')
    .select('trigger_entity_type, trigger_entity_id')
    .eq('id', executionId)
    .single();

  if (execError) throw execError;

  let leadId = execution.trigger_entity_id;
  
  // If triggered by case, get lead ID from case
  if (execution.trigger_entity_type === 'case') {
    const { data: caseData, error: caseError } = await supabaseAdmin
      .from('cases')
      .select('lead_id')
      .eq('id', execution.trigger_entity_id)
      .single();
    
    if (caseError) throw caseError;
    leadId = caseData.lead_id;
  }

  // Update lead status
  const { error: updateError } = await supabaseAdmin
    .from('leads')
    .update({ status: config.new_status })
    .eq('id', leadId);

  if (updateError) throw updateError;

  return { lead_id: leadId, new_status: config.new_status };
}

async function executeUpdateCaseStatus(supabaseAdmin: any, executionId: string, config: any) {
  // Get execution context
  const { data: execution, error: execError } = await supabaseAdmin
    .from('workflow_executions')
    .select('trigger_entity_type, trigger_entity_id')
    .eq('id', executionId)
    .single();

  if (execError) throw execError;

  if (execution.trigger_entity_type !== 'case') {
    throw new Error('Cannot update case status when triggered by lead');
  }

  // Update case status
  const { error: updateError } = await supabaseAdmin
    .from('cases')
    .update({ status: config.new_status })
    .eq('id', execution.trigger_entity_id);

  if (updateError) throw updateError;

  return { case_id: execution.trigger_entity_id, new_status: config.new_status };
}

async function executeCreateCase(supabaseAdmin: any, executionId: string, config: any) {
  // Get execution context
  const { data: execution, error: execError } = await supabaseAdmin
    .from('workflow_executions')
    .select('trigger_entity_type, trigger_entity_id, company_id')
    .eq('id', executionId)
    .single();

  if (execError) throw execError;

  let leadId = execution.trigger_entity_id;
  
  // If triggered by case, get lead ID from case
  if (execution.trigger_entity_type === 'case') {
    const { data: caseData, error: caseError } = await supabaseAdmin
      .from('cases')
      .select('lead_id')
      .eq('id', execution.trigger_entity_id)
      .single();
    
    if (caseError) throw caseError;
    leadId = caseData.lead_id;
  }

  // Get a user from the company to assign the case to
  const { data: companyUser, error: userError } = await supabaseAdmin
    .from('user_roles')
    .select('user_id')
    .eq('company_id', execution.company_id)
    .eq('role', 'company_admin')
    .limit(1)
    .single();

  let assignedUserId = companyUser?.user_id;

  if (userError || !assignedUserId) {
    // Fallback: get any user from the company
    const { data: anyUser } = await supabaseAdmin
      .from('user_roles')
      .select('user_id')
      .eq('company_id', execution.company_id)
      .limit(1)
      .single();

    if (!anyUser) {
      throw new Error('No users found in company to assign case to');
    }
    assignedUserId = anyUser.user_id;
  }

  // Replace variables in title
  let title = config.title || 'תיק חדש';

  // Get lead data for variable replacement
  const { data: leadData } = await supabaseAdmin
    .from('leads')
    .select('full_name, phone, email, status, value, source, notes, assigned_user_id')
    .eq('id', leadId)
    .single();

  // Get company data for variables
  const { data: companyData } = await supabaseAdmin
    .from('companies')
    .select('name, email, phone, address')
    .eq('id', execution.company_id)
    .single();

  // Get assigned user data for variables (if available)
  let assignedUserData = null;
  if (leadData?.assigned_user_id) {
    const { data: userData, error: userError } = await supabaseAdmin.auth.admin.getUserById(leadData.assigned_user_id);
    if (!userError && userData.user) {
      assignedUserData = {
        full_name: userData.user.user_metadata?.full_name || '',
        email: userData.user.email || '',
        phone: userData.user.user_metadata?.phone || userData.user.phone || ''
      };
    }
  }

  if (leadData) {
    // Lead variables
    title = title.replace(/\{lead_name\}/g, leadData.full_name || '');
    title = title.replace(/\{lead_phone\}/g, leadData.phone || '');
    title = title.replace(/\{lead_email\}/g, leadData.email || '');
    title = title.replace(/\{lead_status\}/g, leadData.status || '');
    title = title.replace(/\{lead_value\}/g, leadData.value ? `₪${leadData.value}` : '');
    title = title.replace(/\{lead_source\}/g, leadData.source || '');
    title = title.replace(/\{lead_notes\}/g, leadData.notes || '');
  }

  if (companyData) {
    // Company variables
    title = title.replace(/\{company_name\}/g, companyData.name || '');
    title = title.replace(/\{company_email\}/g, companyData.email || '');
    title = title.replace(/\{company_phone\}/g, companyData.phone || '');
    title = title.replace(/\{company_address\}/g, companyData.address || '');
  }

  if (assignedUserData) {
    // Assigned user variables
    title = title.replace(/\{assigned_user_name\}/g, assignedUserData.full_name || '');
    title = title.replace(/\{assigned_user_email\}/g, assignedUserData.email || '');
    title = title.replace(/\{assigned_user_phone\}/g, assignedUserData.phone || '');
  } else {
    // Replace with empty strings if no assigned user
    title = title.replace(/\{assigned_user_name\}/g, '');
    title = title.replace(/\{assigned_user_email\}/g, '');
    title = title.replace(/\{assigned_user_phone\}/g, '');
  }

  // Create new case
  const caseData = {
    company_id: execution.company_id,
    lead_id: leadId,
    assigned_user_id: assignedUserId,
    title: title,
    description: config.description || null,
    case_type_id: config.case_type_id,
    status: config.status || 'בקליטה',
    value: config.value || null,
    deadline: config.deadline || null
  };

  const { data: newCase, error: createError } = await supabaseAdmin
    .from('cases')
    .insert(caseData)
    .select()
    .single();

  if (createError) throw createError;

  return { case_id: newCase.id, case_data: caseData };
}

async function moveToNextStep(supabaseAdmin: any, executionId: string, currentStepOrder: number) {
  // Get workflow and check if there are more steps
  const { data: execution, error: execError } = await supabaseAdmin
    .from('workflow_executions')
    .select(`
      *,
      workflows!inner (
        workflow_steps (step_order)
      )
    `)
    .eq('id', executionId)
    .single();

  if (execError) throw execError;

  const maxStepOrder = Math.max(...execution.workflows.workflow_steps.map((s: any) => s.step_order));

  if (currentStepOrder >= maxStepOrder) {
    // Workflow completed
    await supabaseAdmin
      .from('workflow_executions')
      .update({
        status: 'completed',
        completed_at: new Date().toISOString(),
        next_execution_at: null
      })
      .eq('id', executionId);
  } else {
    // Check if we have a scheduled next execution time (from wait step)
    if (execution.next_execution_at) {
      // Don't execute next step immediately - let scheduler handle it
      await supabaseAdmin
        .from('workflow_executions')
        .update({
          current_step_order: currentStepOrder + 1
          // Keep next_execution_at as is - don't clear it
        })
        .eq('id', executionId);

      console.log(`Workflow execution ${executionId} scheduled to continue at ${execution.next_execution_at}`);
    } else {
      // No wait time, execute next step immediately
      await supabaseAdmin
        .from('workflow_executions')
        .update({
          current_step_order: currentStepOrder + 1,
          next_execution_at: null
        })
        .eq('id', executionId);

      // Execute next step immediately
      const { data: nextStep, error: stepError } = await supabaseAdmin
        .from('workflow_steps')
        .select('*')
        .eq('workflow_id', execution.workflow_id)
        .eq('step_order', currentStepOrder + 1)
        .single();

      if (!stepError && nextStep) {
        await executeWorkflowStep(supabaseAdmin, executionId, nextStep);
      }
    }
  }
}

async function retryFailedExecution(supabaseAdmin: any, executionId: string) {
  // Reset execution status and retry
  const { data: execution, error: execError } = await supabaseAdmin
    .from('workflow_executions')
    .update({
      status: 'running',
      error_message: null
    })
    .eq('id', executionId)
    .select(`
      *,
      workflows!inner (
        workflow_steps (*)
      )
    `)
    .single();

  if (execError) throw execError;

  // Find current step and retry
  const currentStep = execution.workflows.workflow_steps.find(
    (step: any) => step.step_order === execution.current_step_order
  );

  if (currentStep) {
    await executeWorkflowStep(supabaseAdmin, executionId, currentStep);
  }

  return new Response(
    JSON.stringify({ success: true, execution_id: executionId }),
    { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  );
}

async function continueExecution(supabaseAdmin: any, executionId: string) {
  try {
    // Get the execution
    const { data: execution, error: execError } = await supabaseAdmin
      .from('workflow_executions')
      .select(`
        *,
        workflows!inner (
          workflow_steps (*)
        )
      `)
      .eq('id', executionId)
      .single();

    if (execError || !execution) {
      throw new Error('Execution not found');
    }

    console.log(`Continuing execution ${executionId} from step ${execution.current_step_order}`);

    // Clear the next execution time
    await supabaseAdmin
      .from('workflow_executions')
      .update({
        next_execution_at: null
      })
      .eq('id', executionId);

    // Get the current step and execute it
    const currentStep = execution.workflows.workflow_steps.find(
      (step: any) => step.step_order === execution.current_step_order
    );

    if (currentStep) {
      await executeWorkflowStep(supabaseAdmin, executionId, currentStep);
    } else {
      console.error(`Step ${execution.current_step_order} not found for execution ${executionId}`);
    }

    return new Response(
      JSON.stringify({ success: true, execution_id: executionId }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Error continuing execution:', error);
    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
}

// New function to handle manual workflow triggers
async function triggerManualWorkflow(supabaseAdmin: any, manualTriggerData: any) {
  const { workflow_id, target_statuses, company_id } = manualTriggerData;

  console.log(`Triggering manual workflow ${workflow_id} for statuses:`, target_statuses);

  try {
    // Get the workflow details
    const { data: workflow, error: workflowError } = await supabaseAdmin
      .from('workflows')
      .select('*')
      .eq('id', workflow_id)
      .eq('company_id', company_id)
      .eq('is_active', true)
      .eq('trigger_type', 'manual')
      .single();

    if (workflowError || !workflow) {
      throw new Error(`Manual workflow not found or inactive: ${workflow_id}`);
    }

    // Get leads matching the target statuses
    const { data: leads, error: leadsError } = await supabaseAdmin
      .from('leads')
      .select('id, full_name, status')
      .eq('company_id', company_id)
      .in('status', target_statuses);

    if (leadsError) {
      throw new Error(`Error fetching leads: ${leadsError.message}`);
    }

    if (!leads || leads.length === 0) {
      return new Response(
        JSON.stringify({
          success: true,
          message: 'No leads found matching the specified statuses',
          leads_processed: 0
        }),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Create workflow executions for each lead
    const executions = [];
    const errors = [];

    for (const lead of leads) {
      try {
        // Check if there's already a running execution for this lead and workflow
        const { data: existingExecution } = await supabaseAdmin
          .from('workflow_executions')
          .select('id')
          .eq('workflow_id', workflow_id)
          .eq('trigger_entity_id', lead.id)
          .eq('status', 'running')
          .single();

        if (existingExecution) {
          console.log(`Skipping lead ${lead.id} - already has running execution`);
          continue;
        }

        // Create new execution
        const { data: execution, error: executionError } = await supabaseAdmin
          .from('workflow_executions')
          .insert({
            workflow_id: workflow_id,
            company_id: company_id,
            trigger_entity_type: 'lead',
            trigger_entity_id: lead.id,
            status: 'running',
            current_step_order: 1,
            started_at: new Date().toISOString()
          })
          .select()
          .single();

        if (executionError) {
          errors.push(`Lead ${lead.full_name}: ${executionError.message}`);
          continue;
        }

        executions.push({
          execution_id: execution.id,
          lead_id: lead.id,
          lead_name: lead.full_name
        });

        // Log the execution start
        await supabaseAdmin
          .from('workflow_execution_logs')
          .insert({
            execution_id: execution.id,
            step_id: null,
            step_order: 0,
            step_type: 'manual_trigger',
            status: 'completed',
            input_data: {
              trigger_type: 'manual',
              target_statuses: target_statuses,
              lead_status: lead.status
            },
            output_data: {
              message: 'Manual workflow triggered successfully'
            }
          });

      } catch (error) {
        errors.push(`Lead ${lead.full_name}: ${error.message}`);
      }
    }

    // Start processing the first step for all executions
    for (const execution of executions) {
      try {
        // Get the first step of the workflow
        const { data: firstStep, error: stepError } = await supabaseAdmin
          .from('workflow_steps')
          .select('*')
          .eq('workflow_id', workflow_id)
          .eq('step_order', 1)
          .single();

        if (!stepError && firstStep) {
          // Execute the first step asynchronously
          await executeWorkflowStep(supabaseAdmin, execution.execution_id, firstStep);
        }
      } catch (error) {
        console.error(`Error starting execution ${execution.execution_id}:`, error);
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: `Manual workflow triggered for ${executions.length} leads`,
        leads_processed: executions.length,
        total_leads_found: leads.length,
        executions: executions,
        errors: errors.length > 0 ? errors : undefined
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Error in triggerManualWorkflow:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        leads_processed: 0
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
}

Deno.serve(handler);
